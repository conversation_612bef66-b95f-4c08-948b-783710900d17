# 业务逻辑判断问题分析报告

## 问题描述
工作流生成的代码在业务逻辑判断时存在固定模式错误：
- 用户要求：光照强度 > 50lux 且距离 < 30cm 时触发蜂鸣器
- 实际生成：光照强度 < 100lux 且距离 < 50cm 的判断逻辑

## 根本原因分析

### 1. 模型Temperature配置问题
**文件**: `app/langgraph_def/graph_builder.py` (第47-54行)
```python
system_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.5, api_key=API_KEY, base_url=BASE_URL)
module_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.4, api_key=API_KEY, base_url=BASE_URL)
api_designer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.3, api_key=API_KEY, base_url=BASE_URL)
developer_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.2, api_key=API_KEY, base_url=BASE_URL)
```

**问题**: `system_architect_model` 的temperature=0.5过高，导致描述重写时引入随机性

### 2. 描述重写节点的逻辑保护失效
**文件**: `app/langgraph_def/graph_builder.py` (第1162-1230行)

**完整的重写prompt代码**:
```python
rewrite_prompt = textwrap.dedent(f"""
<Prompt>
    <Role>You are a technical writer specializing in embedded systems. Your task is to rewrite a high-level functional description into a precise, unambiguous technical specification in English for a developer.</Role>
    <Goal>Combine the device's core function with its specific communication tasks and contract constraints into a single, clear paragraph in English, WITHOUT altering the core logic.</Goal>
    <Context>
        <DeviceRole>{role}</DeviceRole>
        <OriginalDescription>{original_description}</OriginalDescription>
        <TechnicalCommunicationPlan>{comm_context}</TechnicalCommunicationPlan>
        {contract_constraints}
    </Context>
    <Instructions>
        <Rule ID="CRITICAL_LOGIC_PRESERVATION" Priority="ABSOLUTE">
        CRITICAL RULE: You MUST identify and preserve all specific numerical values, comparison operators (>, <, ==, etc.), and logical operators (AND, OR, NOT) from the original description. These rules must be explicitly listed in a structured format, such as a <LogicRules> XML tag, within your rewritten output. Do NOT paraphrase or simplify them.
        </Rule>

        <Rule ID="LOGIC_PRESERVATION" Priority="CRITICAL">
        1.  **DO NOT CHANGE THE CORE LOGIC**: You MUST preserve the exact conditional logic from the `<OriginalDescription>`.
        2.  **PRESERVE THRESHOLDS**: Keep all numerical thresholds (e.g., `50lux`, `50cm`) exactly as they are.
        3.  **PRESERVE OPERATORS**: Keep all logical operators (`>`, `<`, `==`, `AND`, `OR`) exactly as they are.
        4.  **YOUR JOB IS TO TRANSLATE AND INTEGRATE, NOT REINTERPRET OR INVENT NEW LOGIC.** This is the most important rule. Any modification to the core logic is a critical failure.
        5.  **STRUCTURED LOGIC EXTRACTION**: You MUST extract all logical conditions and numerical thresholds into a <LogicRules> section within your output to ensure they are explicitly preserved and visible.
        </Rule>
    </Instructions>
</Prompt>
""")

response = system_architect_model.invoke([HumanMessage(content=rewrite_prompt)])
new_description = response.content.strip()
task['description'] = new_description
```

**问题**: 尽管有明确规则，但AI模型仍可能在重写过程中改变数值

### 3. 原始需求备份机制存在缺陷
**文件**: `app/langgraph_def/graph_builder.py` (第1116-1130行)

**备份和重写的完整流程**:
```python
# 2. Generate task contracts and rewrite descriptions for each device
for task in device_tasks:
    role = task.get('device_role')
    original_description = task.get('description', '')
    # =================================================================================
    # 核心修改：在重写描述之前，将原始的中文需求备份到新字段中
    # =================================================================================
    task['original_description_for_developer'] = original_description

    peripherals = task.get('peripherals', [])

    # Generate task contract
    task_contract = generate_task_contract(role, peripherals, topic_map)
    task['task_contract'] = task_contract

    # ... 然后进行重写 ...
    response = system_architect_model.invoke([HumanMessage(content=rewrite_prompt)])
    new_description = response.content.strip()
    task['description'] = new_description
```

**问题**: 备份发生在重写之前，但如果原始description已经被污染，备份也是错误的

### 4. 开发者节点的双重输入冲突
**文件**: `app/langgraph_def/graph_builder.py` (第2179-2189行, 第2588-2589行)

**原始需求上下文构建代码**:
```python
# 1. 从任务状态中获取我们之前备份的、未被修改的原始中文需求
original_description = task.get('original_description_for_developer', '')
original_requirement_context = ""
if original_description:
    original_requirement_context = textwrap.dedent(f"""
    <OriginalRequirement_SourceOfTruth>
    --- MANDATORY BUSINESS LOGIC (USER'S ORIGINAL REQUEST) ---
    {original_description}
    --- END MANDATORY BUSINESS LOGIC ---
    </OriginalRequirement_SourceOfTruth>
    """)
```

**开发者prompt中的冲突指令**:
```python
prompt = textwrap.dedent(f"""
    <Architectural_Mandates>
        1.  **Source of Truth for Logic (ULTRA-CRITICAL):** The `<OriginalRequirement_SourceOfTruth>` block contains the user's original, unmodified request. This block is the **ABSOLUTE AND ONLY SOURCE OF TRUTH** for business logic (if-else conditions, thresholds, triggers). You MUST implement the logic described in this block precisely. The `<TaskDescription>` block is for general context and generating comments ONLY. If there is any conflict between the two, the `<OriginalRequirement_SourceOfTruth>` block ALWAYS WINS.
    </Architectural_Mandates>

    <Context>
        {original_requirement_context}  # 来自备份的原始需求
        <TaskDescription>{original_description}</TaskDescription>  # 来自重写后的描述
    </Context>
""")
```

**问题**: 如果两个描述不一致，AI可能选择错误的源

### 5. API包中的硬编码默认值影响
**多个API包文件中存在默认阈值**:
- `50101BS_API_Package.json`: `"default_brightness": 50`
- `SM4205_API_Package.json`: `"default_brightness": 50`
- `DS1624_API_Package.json`: `"timeout_threshold_ms": 1000`

**问题**: AI可能受到这些默认值影响，倾向于使用50、100等"常见"数值

### 6. 示例代码中的固定模式
**文件**: `app/langgraph_def/graph_builder.py` (第1197-1216行)

**完整的示例代码**:
```python
<Example>
    <Input>
        <OriginalDescription>接收来自大绿板的光照强度和距离数据，如果光照强度高于50lux且距离小于50cm，立即激活5号引脚的蜂鸣器并持续鸣响(LOW电平激活，HIGH电平关闭)，同时通过MQTT协议向涂鸦云平台发送报警信息。当光照强度低于50lux或距离大于等于50cm时关闭蜂鸣器。</OriginalDescription>
        <TechnicalCommunicationPlan>It MUST subscribe to the following MQTT topics to receive data: /smart_system/light_collector/data.</TechnicalCommunicationPlan>
        <ContractConstraints>
        CRITICAL: This device MUST NOT perform any local sensor reading.
        </ContractConstraints>
    </Input>
    <Output>
    Subscribe to the MQTT topic /smart_system/light_collector/data to receive light intensity and distance values.

    <LogicRules>
    - Activation condition: light intensity > 50 lux AND distance < 50cm
    - Deactivation condition: light intensity <= 50 lux OR distance >= 50cm
    - Pin configuration: Pin 5 (active LOW, inactive HIGH)
    </LogicRules>

    If the light intensity is greater than 50 lux AND the distance is less than 50cm, immediately activate the buzzer on pin 5 (active LOW) and send an alarm message to the Tuya Cloud. When the light intensity is less than or equal to 50 lux OR the distance is greater than or equal to 50cm, deactivate the buzzer. This device operates as a data consumer and must not perform any local sensor readings.
    </Output>
</Example>
```

**问题**: 示例使用了50lux和50cm，可能导致AI产生模式记忆，总是倾向于使用这些数值

## 修复建议

### 1. 降低system_architect_model的temperature
**修改位置**: `app/langgraph_def/graph_builder.py` 第47行
```python
# 当前代码
system_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.5, api_key=API_KEY, base_url=BASE_URL)

# 修改为
system_architect_model = ChatOpenAI(model="kimi-k2-250711", temperature=0.1, api_key=API_KEY, base_url=BASE_URL)
```

### 2. 在更早阶段备份原始需求
**修改位置**: `app/services/project_analyzer_service.py`
在`analyze_requirement`函数中，在调用AI之前就备份用户的原始输入

### 3. 增强数值验证机制
**修改位置**: `app/langgraph_def/graph_builder.py` 第1230行后添加
```python
# 在描述重写后添加数值一致性检查
def validate_numerical_consistency(original, rewritten):
    import re
    original_numbers = re.findall(r'\d+(?:\.\d+)?', original)
    rewritten_numbers = re.findall(r'\d+(?:\.\d+)?', rewritten)
    return set(original_numbers) == set(rewritten_numbers)
```

### 4. 移除或修改示例中的固定数值
**修改位置**: `app/langgraph_def/graph_builder.py` 第1198-1216行
将示例中的具体数值改为变量形式：`{threshold_value}lux`、`{distance_value}cm`

### 5. 添加契约验证层
在开发者节点生成代码后，验证生成的条件判断是否与原始需求一致

### 6. 优化prompt结构
明确指定哪个输入源具有最高优先级，避免AI在多个输入间产生混淆
